#!/bin/bash

# Shotgun Code with Ollama Integration - Launcher <PERSON>
# This script handles common display and environment issues

echo "🚀 Starting Shotgun Code with Ollama Integration..."

# Set the script directory as working directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Check if binary exists
if [ ! -f "./build/bin/shotgun-code" ]; then
    echo "❌ Error: shotgun-code binary not found at ./build/bin/shotgun-code"
    echo "Please build the application first with: wails build"
    exit 1
fi

# Note: JSC_SIGNAL_FOR_GC causes errors in some WebKit versions, so we'll skip it
# The signal override warning is harmless and can be ignored

# Function to try different display methods
try_display_methods() {
    echo "🖥️  Attempting to start with display support..."

    # Method 1: Try with current DISPLAY if set
    if [ -n "$DISPLAY" ]; then
        echo "   Trying with current DISPLAY=$DISPLAY..."
        if command -v xset >/dev/null 2>&1 && xset q >/dev/null 2>&1; then
            echo "✅ Starting with current display..."
            exec ./build/bin/shotgun-code
            return 0
        fi
    fi

    # Method 2: Try with DISPLAY=:0
    echo "   Trying with DISPLAY=:0..."
    if command -v xset >/dev/null 2>&1 && DISPLAY=:0 xset q >/dev/null 2>&1; then
        echo "✅ Starting with DISPLAY=:0..."
        export DISPLAY=:0
        exec ./build/bin/shotgun-code
        return 0
    fi

    # Method 3: Try with DISPLAY=:1 (common for GNOME)
    echo "   Trying with DISPLAY=:1..."
    if command -v xset >/dev/null 2>&1 && DISPLAY=:1 xset q >/dev/null 2>&1; then
        echo "✅ Starting with DISPLAY=:1..."
        export DISPLAY=:1
        exec ./build/bin/shotgun-code
        return 0
    fi

    # Method 4: Try with xvfb (virtual display)
    echo "   Trying with virtual display (xvfb)..."
    if command -v xvfb-run >/dev/null 2>&1; then
        echo "✅ Starting with virtual display..."
        echo ""
        echo "🎉 Application is starting! You may see some harmless warnings:"
        echo "   - 'Overriding existing handler for signal' (normal WebKit behavior)"
        echo "   - 'libEGL warning: DRI3' (normal for virtual displays)"
        echo "   - These warnings don't affect functionality"
        echo ""
        echo "🖥️  The GUI should appear shortly..."
        exec xvfb-run -a ./build/bin/shotgun-code
        return 0
    else
        echo "⚠️  xvfb not found. Installing..."
        if command -v apt >/dev/null 2>&1; then
            sudo apt update && sudo apt install -y xvfb
            if [ $? -eq 0 ]; then
                echo "✅ Starting with virtual display..."
                exec xvfb-run -a ./build/bin/shotgun-code
                return 0
            fi
        fi
    fi

    # Method 5: WSL detection and setup
    if grep -qi microsoft /proc/version 2>/dev/null; then
        echo "   Detected WSL environment..."
        if command -v cmd.exe >/dev/null 2>&1; then
            echo "✅ Setting up WSL display..."
            export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
            exec ./build/bin/shotgun-code
            return 0
        fi
    fi

    return 1
}

# Check for common issues
echo "🔍 Checking system requirements..."

# Check if running as root (not recommended)
if [ "$EUID" -eq 0 ]; then
    echo "⚠️  Warning: Running as root is not recommended for GUI applications"
    echo "   This may cause graphics permission issues"
    echo "   Consider running as a regular user instead"
fi

# Check for Ollama
echo "🤖 Checking Ollama availability..."
if command -v ollama >/dev/null 2>&1; then
    echo "✅ Ollama CLI found"

    # Check if Ollama service is running
    if curl -s http://*************:11434/api/tags >/dev/null 2>&1; then
        echo "✅ Ollama service is running at http://*************:11434"
    elif curl -s http://localhost:11434/api/tags >/dev/null 2>&1; then
        echo "✅ Ollama service is running at http://localhost:11434"
        echo "ℹ️  Note: You may need to update the URL in the application settings"
    else
        echo "⚠️  Ollama service not detected. You may need to start it:"
        echo "   ollama serve"
    fi

    # Check for devstral model on the remote server
    if curl -s http://*************:11434/api/tags | grep -q "devstral"; then
        echo "✅ devstral model is available on remote server"
    elif curl -s http://localhost:11434/api/tags | grep -q "devstral" 2>/dev/null; then
        echo "✅ devstral model is available on local server"
    else
        echo "⚠️  devstral model not found on Ollama server"
        echo "   💡 Make sure the model is available on the Ollama server:"
        echo "   ollama pull devstral:latest"
    fi
else
    echo "⚠️  Ollama not found. Install it from: https://ollama.ai"
fi

echo ""
echo "🎯 Starting application..."

# Try different display methods
if try_display_methods; then
    echo "✅ Application started successfully!"
else
    echo "❌ Failed to start application with any display method"
    echo ""
    echo "💡 Troubleshooting tips:"
    echo "   1. Make sure you have a display server running"
    echo "   2. Try: export DISPLAY=:0 && ./build/bin/shotgun-code"
    echo "   3. For headless servers: xvfb-run -a ./build/bin/shotgun-code"
    echo "   4. For SSH: ssh -X username@hostname"
    echo "   5. Check the ollama-readme.md for more solutions"
    exit 1
fi
