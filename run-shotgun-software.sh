#!/bin/bash

echo "🚀 Starting Shotgun Code with Software Rendering"
echo "==============================================="

cd "$(dirname "$0")"

# Force software rendering to avoid graphics permission issues
export LIBGL_ALWAYS_SOFTWARE=1
export GALLIUM_DRIVER=llvmpipe
export MESA_GL_VERSION_OVERRIDE=3.3
export WEBKIT_DISABLE_COMPOSITING_MODE=1

# Set up X11 authentication if needed
if [ -f "/run/user/$(id -u)/gdm/Xauthority" ]; then
    export XAUTHORITY="/run/user/$(id -u)/gdm/Xauthority"
fi

echo "📍 Working directory: $(pwd)"
echo "🔍 Current user: $(whoami)"
echo "🖥️  Display: $DISPLAY"
echo "🎨 Graphics: Software rendering enabled"
echo ""

# Check if binary exists
if [ ! -f "./build/bin/shotgun-code" ]; then
    echo "❌ Error: shotgun-code binary not found"
    exit 1
fi

echo "✅ Binary found"
echo ""

# Check Ollama quickly
echo "🤖 Checking Ollama..."
if curl -s http://*************:11434/api/tags >/dev/null 2>&1; then
    echo "✅ Ollama service accessible"
else
    echo "⚠️  Ollama service not accessible (you can still use other features)"
fi

echo ""
echo "🚀 Starting application with software rendering..."
echo "   This should avoid graphics permission issues"
echo ""

# Start the application
exec ./build/bin/shotgun-code
