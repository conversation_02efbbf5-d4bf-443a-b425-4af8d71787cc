# Code Guide: Refactoring Shotgun-Code for Modular CLI and Ollama Integration

## Introduction

This guide provides step-by-step instructions for refactoring the existing Wails-based `shotgun_code` application into a standalone Go command-line interface (CLI) tool that integrates with a local Ollama instance. The new CLI tool will maintain the core functionality of generating structured project context while adding direct integration with the devstral:latest model running at http://*************:11434/v1.

**Benefits of this refactoring:**
- **Scriptability**: Enable automation and integration into CI/CD pipelines
- **Local LLM Usage**: Direct integration with Ollama for privacy and offline processing
- **Modular Design**: Clean separation of concerns for better maintainability
- **Performance**: Eliminate GUI overhead for faster processing

## Part 1: Understanding Core Logic in the Original shotgun_code

### Key Components in app.go

The original application contains several essential components that need to be adapted:

#### Core Data Structures
```go
// FileNode represents a file or directory in the project tree
type FileNode struct {
    Name            string      `json:"name"`
    Path            string      `json:"path"`    // Full path
    RelPath         string      `json:"relPath"` // Path relative to selected root
    IsDir           bool        `json:"isDir"`
    Children        []*FileNode `json:"children,omitempty"`
    IsGitignored    bool        `json:"isGitignored"`
    IsCustomIgnored bool        `json:"isCustomIgnored"`
}

// AppSettings holds configuration for ignore rules
type AppSettings struct {
    CustomIgnoreRules string `json:"customIgnoreRules"`
    CustomPromptRules string `json:"customPromptRules"`
}
```

#### Essential Functions to Adapt

1. **`ListFiles(dirPath string) ([]*FileNode, error)`**: Scans directory and builds file tree with ignore rule processing
2. **`buildTreeRecursive()`**: Recursively builds the file tree structure
3. **`generateShotgunOutputWithProgress()`**: Creates the final "shotgun" output format
4. **`compileCustomIgnorePatterns()`**: Compiles custom ignore rules using gitignore syntax

#### Dependencies to Remove
- `github.com/wailsapp/wails/v2/pkg/runtime`: Used for logging and UI events
- Wails context and event emission calls
- GUI-specific progress reporting mechanisms

### Output Format
The application generates a specific text format:
1. **Directory tree** using ASCII art (├──, └──, │)
2. **File contents** wrapped in XML-like tags: `<file path="relative/path">content</file>`

## Part 2: Designing the New Go CLI Application (shotgun-cli)

### Proposed Go Project Structure
```
shotgun-cli/
├── main.go                 # CLI entry point and argument parsing
├── go.mod                  # Module definition
├── go.sum                  # Dependency checksums
├── pkg/
│   ├── config/
│   │   └── config.go       # Configuration management
│   ├── contextgen/
│   │   └── generator.go    # Context generation logic
│   ├── ignore/
│   │   └── rules.go        # Ignore rule processing
│   └── ollama/
│       └── client.go       # Ollama API client
└── README.md               # Usage documentation
```

### Key Data Structures for CLI

```go
// Config holds CLI configuration
type Config struct {
    Directory       string
    Task           string
    IgnoreFile     string
    OllamaURL      string
    Model          string
    MaxSize        int64
    UseGitignore   bool
    UseCustomIgnore bool
}

// FileNode adapted for CLI (simplified)
type FileNode struct {
    Name            string
    Path            string
    RelPath         string
    IsDir           bool
    Children        []*FileNode
    IsGitignored    bool
    IsCustomIgnored bool
}
```

### CLI Arguments Design
```bash
shotgun-cli [OPTIONS]

Options:
  -dir <path>           Project directory to analyze (default: current directory)
  -task "<prompt>"      Task description for the LLM
  -ignorefile <path>    Custom ignore rules file (optional)
  -ollamaurl <url>      Ollama API URL (default: http://*************:11434/v1)
  -model <name>         Ollama model name (default: devstral:latest)
  -maxsize <bytes>      Maximum context size in bytes (default: 10000000)
  -gitignore           Use .gitignore rules (default: true)
  -customignore        Use custom ignore rules (default: true)
  -help                Show this help message
```

## Part 3: Step-by-Step Implementation Guide

### 3.1. Setting Up the New Go Project

```bash
# Create new project directory
mkdir shotgun-cli
cd shotgun-cli

# Initialize Go module
go mod init shotgun-cli

# Create directory structure
mkdir -p pkg/config pkg/contextgen pkg/ignore pkg/ollama

# Create main.go
touch main.go

# Create package files
touch pkg/config/config.go
touch pkg/contextgen/generator.go
touch pkg/ignore/rules.go
touch pkg/ollama/client.go
```

### 3.2. Implementing Ignore Rule Logic (pkg/ignore/rules.go)

```go
package ignore

import (
    "os"
    "path/filepath"
    "strings"

    gitignore "github.com/sabhiram/go-gitignore"
)

// IgnoreManager handles both .gitignore and custom ignore patterns
type IgnoreManager struct {
    projectGitignore *gitignore.GitIgnore
    customIgnore     *gitignore.GitIgnore
    useGitignore     bool
    useCustomIgnore  bool
}

// NewIgnoreManager creates a new ignore manager
func NewIgnoreManager(projectDir string, customIgnoreFile string, useGit, useCustom bool) (*IgnoreManager, error) {
    im := &IgnoreManager{
        useGitignore:    useGit,
        useCustomIgnore: useCustom,
    }

    // Load .gitignore from project directory
    if useGit {
        gitignorePath := filepath.Join(projectDir, ".gitignore")
        if _, err := os.Stat(gitignorePath); err == nil {
            gitIgn, err := gitignore.CompileIgnoreFile(gitignorePath)
            if err != nil {
                return nil, fmt.Errorf("error compiling .gitignore: %w", err)
            }
            im.projectGitignore = gitIgn
        }
    }

    // Load custom ignore rules
    if useCustom && customIgnoreFile != "" {
        if _, err := os.Stat(customIgnoreFile); err == nil {
            customIgn, err := gitignore.CompileIgnoreFile(customIgnoreFile)
            if err != nil {
                return nil, fmt.Errorf("error compiling custom ignore file: %w", err)
            }
            im.customIgnore = customIgn
        }
    }

    return im, nil
}

// IsIgnored checks if a path should be ignored
func (im *IgnoreManager) IsIgnored(relPath string, isDir bool) (bool, bool) {
    pathToMatch := relPath
    if isDir && !strings.HasSuffix(pathToMatch, string(os.PathSeparator)) {
        pathToMatch += string(os.PathSeparator)
    }

    isGitignored := false
    isCustomIgnored := false

    if im.useGitignore && im.projectGitignore != nil {
        isGitignored = im.projectGitignore.MatchesPath(pathToMatch)
    }

    if im.useCustomIgnore && im.customIgnore != nil {
        isCustomIgnored = im.customIgnore.MatchesPath(pathToMatch)
    }

    return isGitignored, isCustomIgnored
}

// ShouldSkip returns true if the path should be completely skipped
func (im *IgnoreManager) ShouldSkip(relPath string, isDir bool) bool {
    gitIgnored, customIgnored := im.IsIgnored(relPath, isDir)
    return gitIgnored || customIgnored
}
```

### 3.3. Implementing Context Generation (pkg/contextgen/generator.go)

```go
package contextgen

import (
    "context"
    "fmt"
    "os"
    "path/filepath"
    "sort"
    "strings"

    "shotgun-cli/pkg/ignore"
)

const maxOutputSizeBytes = 10_000_000 // 10MB

// FileNode represents a file or directory in the project tree
type FileNode struct {
    Name            string
    Path            string
    RelPath         string
    IsDir           bool
    Children        []*FileNode
    IsGitignored    bool
    IsCustomIgnored bool
}

// Generator handles context generation
type Generator struct {
    ignoreManager *ignore.IgnoreManager
    maxSize       int64
}

// NewGenerator creates a new context generator
func NewGenerator(ignoreManager *ignore.IgnoreManager, maxSize int64) *Generator {
    if maxSize <= 0 {
        maxSize = maxOutputSizeBytes
    }
    return &Generator{
        ignoreManager: ignoreManager,
        maxSize:       maxSize,
    }
}

// GenerateContext creates the shotgun-style context output
func (g *Generator) GenerateContext(ctx context.Context, rootDir string) (string, error) {
    // Build file tree
    rootNode, err := g.buildFileTree(ctx, rootDir)
    if err != nil {
        return "", fmt.Errorf("failed to build file tree: %w", err)
    }

    // Generate tree string and file contents
    return g.generateOutput(ctx, rootDir, rootNode)
}

// buildFileTree creates the file tree structure
func (g *Generator) buildFileTree(ctx context.Context, rootDir string) (*FileNode, error) {
    rootNode := &FileNode{
        Name:    filepath.Base(rootDir),
        Path:    rootDir,
        RelPath: ".",
        IsDir:   true,
    }

    children, err := g.buildTreeRecursive(ctx, rootDir, rootDir, 0)
    if err != nil {
        return nil, err
    }
    rootNode.Children = children

    return rootNode, nil
}

// buildTreeRecursive recursively builds the file tree (adapted from original)
func (g *Generator) buildTreeRecursive(ctx context.Context, currentPath, rootPath string, depth int) ([]*FileNode, error) {
    select {
    case <-ctx.Done():
        return nil, ctx.Err()
    default:
    }

    entries, err := os.ReadDir(currentPath)
    if err != nil {
        return nil, err
    }

    var nodes []*FileNode
    for _, entry := range entries {
        nodePath := filepath.Join(currentPath, entry.Name())
        relPath, _ := filepath.Rel(rootPath, nodePath)

        // Check if path should be ignored
        isGitignored, isCustomIgnored := g.ignoreManager.IsIgnored(relPath, entry.IsDir())

        node := &FileNode{
            Name:            entry.Name(),
            Path:            nodePath,
            RelPath:         relPath,
            IsDir:           entry.IsDir(),
            IsGitignored:    isGitignored,
            IsCustomIgnored: isCustomIgnored,
        }

        // Skip ignored files/directories
        if g.ignoreManager.ShouldSkip(relPath, entry.IsDir()) {
            continue
        }

        if entry.IsDir() {
            children, err := g.buildTreeRecursive(ctx, nodePath, rootPath, depth+1)
            if err != nil {
                if ctx.Err() != nil {
                    return nil, err
                }
                // Log error but continue with other directories
                fmt.Printf("Warning: Error building subtree for %s: %v\n", nodePath, err)
            } else {
                node.Children = children
            }
        }

        nodes = append(nodes, node)
    }

    // Sort nodes: directories first, then files, then alphabetically
    sort.SliceStable(nodes, func(i, j int) bool {
        if nodes[i].IsDir && !nodes[j].IsDir {
            return true
        }
        if !nodes[i].IsDir && nodes[j].IsDir {
            return false
        }
        return strings.ToLower(nodes[i].Name) < strings.ToLower(nodes[j].Name)
    })

    return nodes, nil
}

// generateOutput creates the final shotgun output format
func (g *Generator) generateOutput(ctx context.Context, rootDir string, rootNode *FileNode) (string, error) {
    var output strings.Builder
    var fileContents strings.Builder

    // Generate tree structure
    output.WriteString(filepath.Base(rootDir) + string(os.PathSeparator) + "\n")

    if err := g.buildTreeString(ctx, rootNode.Children, "", &output, &fileContents, rootDir); err != nil {
        return "", err
    }

    // Check size limit
    totalSize := int64(output.Len() + fileContents.Len())
    if totalSize > g.maxSize {
        return "", fmt.Errorf("context size %d bytes exceeds maximum %d bytes", totalSize, g.maxSize)
    }

    // Combine tree and file contents
    result := output.String() + "\n" + strings.TrimRight(fileContents.String(), "\n")
    return result, nil
}

// buildTreeString recursively builds the tree string and collects file contents
func (g *Generator) buildTreeString(ctx context.Context, nodes []*FileNode, prefix string,
    output, fileContents *strings.Builder, rootDir string) error {

    for i, node := range nodes {
        select {
        case <-ctx.Done():
            return ctx.Err()
        default:
        }

        isLast := i == len(nodes)-1
        branch := "├── "
        nextPrefix := prefix + "│   "
        if isLast {
            branch = "└── "
            nextPrefix = prefix + "    "
        }

        output.WriteString(prefix + branch + node.Name + "\n")

        if node.IsDir {
            if err := g.buildTreeString(ctx, node.Children, nextPrefix, output, fileContents, rootDir); err != nil {
                return err
            }
        } else {
            // Read file content
            content, err := os.ReadFile(node.Path)
            if err != nil {
                content = []byte(fmt.Sprintf("Error reading file: %v", err))
            }

            // Use forward slashes for path attribute
            relPathForwardSlash := filepath.ToSlash(node.RelPath)
            fileContents.WriteString(fmt.Sprintf("<file path=\"%s\">\n", relPathForwardSlash))
            fileContents.WriteString(string(content))
            fileContents.WriteString("\n</file>\n")

            // Check size limit during generation
            if int64(output.Len()+fileContents.Len()) > g.maxSize {
                return fmt.Errorf("context size limit exceeded during generation")
            }
        }
    }

    return nil
}
```

### 3.4. Implementing Ollama Client (pkg/ollama/client.go)

```go
package ollama

import (
    "bytes"
    "context"
    "encoding/json"
    "fmt"
    "io"
    "net/http"
    "time"
)

// Client handles communication with Ollama API
type Client struct {
    baseURL    string
    httpClient *http.Client
}

// NewClient creates a new Ollama client
func NewClient(baseURL string) *Client {
    return &Client{
        baseURL: baseURL,
        httpClient: &http.Client{
            Timeout: 300 * time.Second, // 5 minutes for large contexts
        },
    }
}

// GenerateRequest represents the request payload for Ollama
type GenerateRequest struct {
    Model  string `json:"model"`
    Prompt string `json:"prompt"`
    Stream bool   `json:"stream"`
}

// GenerateResponse represents the response from Ollama
type GenerateResponse struct {
    Model     string `json:"model"`
    Response  string `json:"response"`
    Done      bool   `json:"done"`
    Context   []int  `json:"context,omitempty"`
    TotalDuration int64 `json:"total_duration,omitempty"`
    LoadDuration  int64 `json:"load_duration,omitempty"`
    PromptEvalCount int `json:"prompt_eval_count,omitempty"`
    EvalCount     int   `json:"eval_count,omitempty"`
}

// Generate sends a prompt to Ollama and returns the response
func (c *Client) Generate(ctx context.Context, model, prompt string) (string, error) {
    // Prepare request payload
    reqPayload := GenerateRequest{
        Model:  model,
        Prompt: prompt,
        Stream: false, // We want the complete response, not streaming
    }

    jsonData, err := json.Marshal(reqPayload)
    if err != nil {
        return "", fmt.Errorf("failed to marshal request: %w", err)
    }

    // Create HTTP request
    url := c.baseURL + "/api/generate"
    req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonData))
    if err != nil {
        return "", fmt.Errorf("failed to create request: %w", err)
    }

    req.Header.Set("Content-Type", "application/json")

    // Send request
    resp, err := c.httpClient.Do(req)
    if err != nil {
        return "", fmt.Errorf("failed to send request to Ollama: %w", err)
    }
    defer resp.Body.Close()

    // Check response status
    if resp.StatusCode != http.StatusOK {
        body, _ := io.ReadAll(resp.Body)
        return "", fmt.Errorf("Ollama API returned status %d: %s", resp.StatusCode, string(body))
    }

    // Parse response
    var ollamaResp GenerateResponse
    if err := json.NewDecoder(resp.Body).Decode(&ollamaResp); err != nil {
        return "", fmt.Errorf("failed to decode Ollama response: %w", err)
    }

    return ollamaResp.Response, nil
}

// FormatPrompt creates the full prompt combining context and user task
func FormatPrompt(context, userTask string) string {
    return fmt.Sprintf(`%s

---USER TASK---
%s`, context, userTask)
}
```

### 3.5. Building the CLI (main.go)

```go
package main

import (
    "context"
    "flag"
    "fmt"
    "log"
    "os"
    "path/filepath"

    "shotgun-cli/pkg/contextgen"
    "shotgun-cli/pkg/ignore"
    "shotgun-cli/pkg/ollama"
)

// Config holds CLI configuration
type Config struct {
    Directory       string
    Task           string
    IgnoreFile     string
    OllamaURL      string
    Model          string
    MaxSize        int64
    UseGitignore   bool
    UseCustomIgnore bool
    ShowHelp       bool
}

func main() {
    config := parseFlags()

    if config.ShowHelp {
        printUsage()
        return
    }

    if config.Task == "" {
        fmt.Fprintf(os.Stderr, "Error: -task is required\n")
        printUsage()
        os.Exit(1)
    }

    // Ensure directory exists
    if _, err := os.Stat(config.Directory); os.IsNotExist(err) {
        log.Fatalf("Directory does not exist: %s", config.Directory)
    }

    // Convert to absolute path
    absDir, err := filepath.Abs(config.Directory)
    if err != nil {
        log.Fatalf("Failed to get absolute path: %v", err)
    }
    config.Directory = absDir

    ctx := context.Background()

    // Initialize ignore manager
    ignoreManager, err := ignore.NewIgnoreManager(
        config.Directory,
        config.IgnoreFile,
        config.UseGitignore,
        config.UseCustomIgnore,
    )
    if err != nil {
        log.Fatalf("Failed to initialize ignore manager: %v", err)
    }

    // Initialize context generator
    generator := contextgen.NewGenerator(ignoreManager, config.MaxSize)

    // Generate context
    fmt.Fprintf(os.Stderr, "Generating context for directory: %s\n", config.Directory)
    context, err := generator.GenerateContext(ctx, config.Directory)
    if err != nil {
        log.Fatalf("Failed to generate context: %v", err)
    }

    fmt.Fprintf(os.Stderr, "Generated context: %d bytes\n", len(context))

    // Initialize Ollama client
    ollamaClient := ollama.NewClient(config.OllamaURL)

    // Format the full prompt
    fullPrompt := ollama.FormatPrompt(context, config.Task)

    // Send to Ollama
    fmt.Fprintf(os.Stderr, "Sending request to Ollama (%s) with model %s...\n", config.OllamaURL, config.Model)
    response, err := ollamaClient.Generate(ctx, config.Model, fullPrompt)
    if err != nil {
        log.Fatalf("Failed to get response from Ollama: %v", err)
    }

    // Output the response to stdout
    fmt.Print(response)
}

func parseFlags() Config {
    config := Config{}

    flag.StringVar(&config.Directory, "dir", ".", "Project directory to analyze")
    flag.StringVar(&config.Task, "task", "", "Task description for the LLM (required)")
    flag.StringVar(&config.IgnoreFile, "ignorefile", "", "Custom ignore rules file")
    flag.StringVar(&config.OllamaURL, "ollamaurl", "http://*************:11434", "Ollama API URL")
    flag.StringVar(&config.Model, "model", "devstral:latest", "Ollama model name")
    flag.Int64Var(&config.MaxSize, "maxsize", 10000000, "Maximum context size in bytes")
    flag.BoolVar(&config.UseGitignore, "gitignore", true, "Use .gitignore rules")
    flag.BoolVar(&config.UseCustomIgnore, "customignore", true, "Use custom ignore rules")
    flag.BoolVar(&config.ShowHelp, "help", false, "Show help message")

    flag.Parse()

    return config
}

func printUsage() {
    fmt.Fprintf(os.Stderr, `shotgun-cli - Generate project context and send to Ollama

Usage: shotgun-cli [OPTIONS]

Options:
  -dir <path>           Project directory to analyze (default: current directory)
  -task "<prompt>"      Task description for the LLM (required)
  -ignorefile <path>    Custom ignore rules file (optional)
  -ollamaurl <url>      Ollama API URL (default: http://*************:11434)
  -model <name>         Ollama model name (default: devstral:latest)
  -maxsize <bytes>      Maximum context size in bytes (default: 10000000)
  -gitignore           Use .gitignore rules (default: true)
  -customignore        Use custom ignore rules (default: true)
  -help                Show this help message

Examples:
  shotgun-cli -task "Fix all TODO comments in the codebase"
  shotgun-cli -dir /path/to/project -task "Add error handling to all functions"
  shotgun-cli -task "Generate unit tests" -model codellama:latest
`)
}
```

## Part 4: Aligning Summaries: Using the CLI with Ollama

### Context Provision
The new CLI tool fundamentally changes how you interact with LLMs compared to the original Wails-based shotgun_code application:

- **Automated Context Generation**: The CLI automatically generates the detailed "shotgun" context (directory tree + file contents) and sends it directly to the Ollama model
- **No Manual Copy-Pasting**: Eliminates the need to manually copy context from a GUI and paste it into external LLM interfaces
- **Streamlined Workflow**: Single command execution replaces the multi-step GUI process

### Prompting Ollama
The CLI constructs the full prompt by combining:
1. **Generated Context**: The complete project structure and file contents in the established shotgun format
2. **User Task**: The specific instruction provided via the `-task` flag
3. **Separator**: A clear `---USER TASK---` delimiter to help the model distinguish between context and instructions

**Example prompt structure sent to Ollama:**
```
project-root/
├── main.go
├── pkg/
│   └── utils/
│       └── helper.go
└── README.md

<file path="main.go">
package main
...
</file>

<file path="pkg/utils/helper.go">
package utils
...
</file>

---USER TASK---
Add comprehensive error handling to all functions in the codebase
```

### Local Processing Benefits
Using the local Ollama instance (devstral:latest at http://*************:11434) provides several advantages:

- **Privacy**: All code and prompts remain on your local network
- **No API Keys**: Eliminates dependency on external services and associated costs
- **Customization**: Ability to fine-tune or swap models based on specific needs
- **Reliability**: No internet dependency or rate limiting concerns
- **Speed**: Direct network access typically faster than external API calls

### Output Interpretation
The CLI outputs the Ollama model's response directly to stdout, enabling flexible usage patterns:

**Direct Usage:**
```bash
shotgun-cli -task "Fix all TODO comments" > fixes.md
```

**Piping for Further Processing:**
```bash
shotgun-cli -task "Generate git diff format patches" | git apply
```

**Integration with Other Tools:**
```bash
shotgun-cli -task "Generate unit tests" | tee tests.go | wc -l
```

### Leveraging Existing Prompt Strategies
You can adapt existing prompt engineering techniques from the original shotgun_code project:

**From prompt_makeDiffGitFormat.md:**
```bash
shotgun-cli -task "Generate a git diff format patch that fixes all linting errors. Follow these rules: 1) Use minimal changes 2) Preserve existing functionality 3) Add comments for complex logic"
```

**From prompt_analyzeBug.md:**
```bash
shotgun-cli -task "Analyze the codebase for potential race conditions and memory leaks. Provide detailed explanations and suggested fixes."
```

**From prompt_makePlan.md:**
```bash
shotgun-cli -task "Create a detailed implementation plan for adding authentication middleware to this web application"
```

The generated context effectively replaces the `{FILE_STRUCTURE}` placeholder used in the original prompt templates.

## Conclusion

The refactored CLI tool provides significant advantages over the original Wails-based application:

### Key Benefits
- **Automation**: Eliminates manual steps in the context generation and LLM interaction process
- **Integration**: Seamlessly fits into development workflows and CI/CD pipelines
- **Performance**: Faster execution without GUI overhead
- **Flexibility**: Easy to script and customize for different use cases
- **Privacy**: Local processing with Ollama ensures code never leaves your environment

### Potential Future Enhancements
- **Configuration Files**: Support for `.shotgun-cli.yaml` files to store project-specific settings
- **Multiple Output Formats**: JSON, XML, or custom formats for different LLM requirements
- **Streaming Support**: Real-time output for long-running Ollama responses
- **Plugin System**: Extensible architecture for custom ignore rules and output processors
- **Batch Processing**: Process multiple directories or tasks in a single run
- **Integration Hooks**: Pre/post-processing scripts for advanced workflows

The new CLI tool maintains the core value proposition of the original shotgun_code application while providing a more efficient, scriptable, and integrated development experience.