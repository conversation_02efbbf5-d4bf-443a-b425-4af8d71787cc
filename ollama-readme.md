# Ollama-Integrated Shotgun Code - Quick Start Guide

## Overview

This is the enhanced version of shotgun_code with integrated Ollama support. You can now generate project context and send it directly to a local LLM (Large Language Model) without any manual copy-pasting.

## Prerequisites

### 1. Ollama Installation & Setup

First, ensure Ollama is installed and running on your system:

```bash
# Install Ollama (if not already installed)
curl -fsSL https://ollama.ai/install.sh | sh

# Pull the devstral model (recommended)
ollama pull devstral:latest

# Start Ollama service
ollama serve
```

**Default Configuration:**
- Ollama URL: `http://*************:11434`
- Model: `devstral:latest`

### 2. System Dependencies (Linux)

If building from source, ensure you have the required dependencies:

```bash
# Install GTK and WebKit dependencies
sudo apt update
sudo apt install -y libgtk-3-dev libwebkit2gtk-4.1-dev

# Install Go (if not already installed)
# Download from https://golang.org/dl/

# Install Wails CLI
go install github.com/wailsapp/wails/v2/cmd/wails@latest
```

## Starting the System

### Option 1: Use the Software Rendering Launcher (Recommended)

If you're having GUI visibility issues, use the software rendering version:

```bash
# Navigate to the shotgun_code directory
cd /path/to/shotgun_code

# Run with software rendering (best for GUI visibility)
./run-shotgun-software.sh
```

### Option 2: Use the Standard Launcher

For automatic display detection:

```bash
# Navigate to the shotgun_code directory
cd /path/to/shotgun_code

# Run the launcher script (handles display issues automatically)
./run-shotgun.sh
```

The launcher script will:
- ✅ Check system requirements
- ✅ Detect and configure display settings automatically
- ✅ Verify Ollama availability
- ✅ Handle common graphics issues
- ✅ Provide helpful troubleshooting information

### Option 3: Run Pre-built Binary Directly

```bash
# Navigate to the shotgun_code directory
cd /path/to/shotgun_code

# Run the application directly
./build/bin/shotgun-code

# If you get graphics errors, try with virtual display:
xvfb-run -a ./build/bin/shotgun-code
```

### Option 4: Build and Run from Source

```bash
# Navigate to the shotgun_code directory
cd /path/to/shotgun_code

# Build the application
wails build

# Run the built application
./build/bin/shotgun-code
```

### Option 5: Development Mode

```bash
# Navigate to the shotgun_code directory
cd /path/to/shotgun_code

# Run in development mode (with hot reload)
wails dev
```

## How to Use the Ollama Integration

### Step 1: Prepare Context
1. **Launch the application**
2. **Select Project Folder**: Click "Select Folder" and choose your project directory
3. **Review File Tree**: The left sidebar shows your project structure
4. **Generate Context**: The system automatically generates the project context
5. **Wait for Completion**: Look for "Context generated successfully" message

### Step 2: Compose Your Request
1. **Navigate to Step 2**: Click on "Compose Prompt" or the step 2 tab
2. **Write Your Task**: In the left panel, describe what you want the AI to do:
   ```
   Example tasks:
   - "Add error handling to all functions in this codebase"
   - "Generate unit tests for the main components"
   - "Refactor this code to use dependency injection"
   - "Find and fix potential security vulnerabilities"
   - "Add comprehensive documentation to all public methods"
   ```
3. **Review Context**: The file structure and content appear in the bottom-left area

### Step 3: Send to Ollama
1. **Switch to Ollama Tab**: Click the "Ollama" tab in the right panel
2. **Verify Settings**:
   - URL: `http://*************:11434` (modify if needed)
   - Model: `devstral:latest` (modify if needed)
3. **Send Request**: Click "Send to Ollama" button
4. **Wait for Response**: You'll see a loading indicator
5. **Review Results**: The AI response appears in the text area
6. **Copy Response**: Use the "Copy" button to copy the response

## Configuration Options

### Ollama Settings
You can modify the Ollama configuration directly in the UI:
- **URL**: Change if Ollama is running on a different host/port
- **Model**: Switch to different models (e.g., `codellama:latest`, `llama2:latest`)

### Project Settings
- **Custom Ignore Rules**: Add custom file patterns to exclude
- **GitIgnore**: Toggle .gitignore rule usage
- **File Filtering**: Manually exclude/include specific files

## Troubleshooting

### Common Issues

#### 1. "Failed to connect to Ollama"
```bash
# Check if Ollama is running
curl http://*************:11434/api/tags

# If not accessible, check the IP and port
# Start Ollama if not running
ollama serve
```

#### 2. "Model not found"
```bash
# Pull the required model
ollama pull devstral:latest

# List available models
ollama list
```

#### 3. "Context too large"
- Reduce project size by adding more ignore patterns
- Exclude large files or directories
- Use custom ignore rules to filter content

#### 4. Application won't start
```bash
# Check dependencies (Linux)
ldd ./build/bin/shotgun-code

# Rebuild if necessary
wails build
```

#### 5. Graphics/Display Issues
If you see errors like "Failed to create GBM buffer" or "Permission denied":

```bash
# DO NOT run with sudo - this causes graphics permission issues
# Instead, run normally:
./build/bin/shotgun-code

# If running over SSH or in headless environment:
# Option A: Enable X11 forwarding
ssh -X username@hostname

# Option B: Use virtual display (for headless servers)
sudo apt install xvfb
xvfb-run -a ./build/bin/shotgun-code

# Option C: Set display environment
export DISPLAY=:0
./build/bin/shotgun-code

# Option D: For WSL users
export DISPLAY=$(cat /etc/resolv.conf | grep nameserver | awk '{print $2}'):0
./build/bin/shotgun-code
```

#### 6. "ERROR" Messages That Are Actually Normal
You may see messages that look like errors but are actually harmless warnings:

```
Overriding existing handler for signal 10. Set JSC_SIGNAL_FOR_GC if you want WebKit to use a different signal
ERROR: invalid option: JSC_SIGNAL_FOR_GC=12
libEGL warning: DRI3: Screen seems not DRI3 capable
```

**These are NOT actual errors!** They are:
- ✅ **Normal WebKit behavior** in virtual display environments
- ✅ **Harmless graphics warnings** that don't affect functionality
- ✅ **Expected output** when running with xvfb

**What to do:** Simply ignore these messages. If the application stays running for more than a few seconds, it's working correctly.

#### 7. GUI Window Not Visible
If the application starts but you don't see the window:

**Symptoms:**
- Process is running (shows in `ps` or task manager)
- No visible window appears
- Graphics permission warnings in console

**Solutions:**
```bash
# Option A: Use software rendering launcher
./run-shotgun-software.sh

# Option B: Force software rendering manually
export LIBGL_ALWAYS_SOFTWARE=1
export WEBKIT_DISABLE_COMPOSITING_MODE=1
./build/bin/shotgun-code

# Option C: Check if window is hidden
# - Press Alt+Tab to cycle through windows
# - Check different workspaces/desktops
# - Look in taskbar or dock
# - Try clicking on the terminal and pressing Alt+Tab
```

### Performance Tips

1. **Optimize Context Size**:
   - Use .gitignore to exclude unnecessary files
   - Add custom ignore patterns for large assets
   - Exclude node_modules, build directories, etc.

2. **Model Selection**:
   - `devstral:latest`: Best for code-related tasks
   - `codellama:latest`: Alternative code-focused model
   - `llama2:latest`: General purpose model

3. **Network Configuration**:
   - Ensure stable network connection to Ollama instance
   - Consider local Ollama installation for best performance

## Advanced Usage

### Custom Ollama Configuration

If you're running Ollama on a different machine or port:

1. **In the Ollama tab**, modify the URL field
2. **Common configurations**:
   - Local: `http://localhost:11434`
   - Remote: `http://your-server-ip:11434`
   - Custom port: `http://*************:8080`

### Batch Processing

For multiple similar tasks:
1. Generate context once
2. Switch between different tasks in the left panel
3. Send each task to Ollama separately
4. Compare and combine responses as needed

### Integration with Development Workflow

1. **Code Review**: Use for analyzing code quality and suggesting improvements
2. **Documentation**: Generate documentation for existing code
3. **Testing**: Create comprehensive test suites
4. **Refactoring**: Get suggestions for code structure improvements
5. **Debugging**: Analyze code for potential bugs and issues

## Support

If you encounter issues:
1. Check the console logs in the bottom panel
2. Verify Ollama is running and accessible
3. Ensure the model is downloaded and available
4. Check network connectivity
5. Review the troubleshooting section above

## Next Steps

- Explore different prompt templates in the "Prompt" tab
- Experiment with different Ollama models
- Customize ignore rules for your specific project types
- Integrate the workflow into your development process

---

**Happy coding with AI assistance!** 🚀
